#ifndef DATAFRAME_H
#define DATAFRAME_H

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>

// Énumération pour les types de colonnes
typedef enum {
    BOOL,       // Type booléen (true/false)
    INT,        // Entier signé
    UINT,       // Entier non signé
    FLOAT,      // Nombre à virgule flottante
    STRING,     // Chaîne de caractères
    UNDEFINED   // Type non déterminé
} column_type_t;

// Structure pour la forme du dataframe
typedef struct {
    int nb_rows;
    int nb_columns;
} dataframe_shape_t;

// Structure principale du dataframe
typedef struct {
    int nb_rows;                    // Nombre de lignes
    int nb_columns;                 // Nombre de colonnes
    char **column_names;            // Noms des colonnes
    column_type_t *column_types;    // Types des colonnes
    void ***data;                   // Données en 2D
} dataframe_t;

// Prototypes des fonctions
dataframe_t *df_read_csv(const char *filename, const char *separator);
void df_info(dataframe_t *dataframe);
dataframe_shape_t df_shape(dataframe_t *dataframe);
void df_free(dataframe_t *dataframe);

// Fonctions utilitaires pour les types
const char *column_type_to_string(column_type_t type);
column_type_t detect_column_type(char **column_data, int nb_rows);

#endif // DATAFRAME_H
