/*
** EPITECH PROJECT, 2025
** gg
** File description:
** gg
*/

#include <stdio.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

typedef enum {
    BOOL,
    INT,
    FLOAT,
    STRING,
    UNDEFINED
} column_type_t;

const char *enum_data(column_type_t type)
{
    if (type == BOOL) {
        return "bool";
    }
    if (type == INT) {
        return "int";
    }
    if (type == FLOAT) {
        return "float";
    }
    if (type == STRING) {
        return "string";
    }
    if (type == UNDEFINED) {
        return "undefined";
    }
    return "unknown";
}

typedef struct {
    int nb_rows;
    int nb_columns;
    char **column_names;
    column_type_t *column_types;
    void ***data;
} dataframe_t;

dataframe_t *create_dataframe(void)
{
    dataframe_t *df = malloc(sizeof(dataframe_t));
    if (df == NULL) {
        return NULL;
    }
    df->nb_rows = 0;
    df->nb_columns = 0;
    df->column_names = NULL;
    df->column_types = NULL;
    df->data = NULL;
    return df;
}

int count_token(char *line, const char *separator)
{
    char *token = NULL;
    int count = 0;

    line[strcspn(line, "\n")] = '\0';
    token = strtok(line, separator);
    while(token != NULL) {
        count++;
        token = strtok(NULL, separator);
    }
    return count;
}

char ** parsing_csv(char *line, const char *separator)
{
    char *token = NULL;
    char *line_copy = strdup(line);
    int i = 0;
    int count = count_token(line_copy, separator);
    char **tokens = malloc(sizeof(char *) * (count + 1));

    line[strcspn(line, "\n")] = '\0';
    token = strtok(line, separator);
    while(token != NULL) {
        tokens[i] = strdup(token);
        token = strtok(NULL, separator);
        i++;
    }
    tokens[i] = NULL;
    free(line_copy);
    return tokens;
}

dataframe_t *df_read_csv(const char *filename, const char *separator)
{
    FILE *file = fopen(filename, "r");
    char **tokens = NULL;
    char buffer[1024];
    dataframe_t *df = create_dataframe();
    df

    if (file == NULL)
        return NULL;
    if (separator == NULL)
        separator = ",";
    while (fgets(buffer, 1024, file) != NULL) {
       tokens = parsing_csv(buffer, separator);
    }
    fclose(file);
    return NULL;
}


int main(void)
{
    df_read_csv("data.csv", ",");
    return 0;
}
