#include "dataframe.h"

// Fonction pour convertir un type en chaîne de caractères
const char *column_type_to_string(column_type_t type) {
    switch (type) {
        case BOOL:
            return "bool";
        case INT:
            return "int";
        case UINT:
            return "uint";
        case FLOAT:
            return "float";
        case STRING:
            return "string";
        case UNDEFINED:
            return "undefined";
        default:
            return "unknown";
    }
}

// Fonction pour détecter le type d'une chaîne
column_type_t detect_string_type(const char *str) {
    if (str == NULL || strlen(str) == 0) {
        return UNDEFINED;
    }
    
    // Test pour booléen
    if (strcmp(str, "true") == 0 || strcmp(str, "false") == 0 ||
        strcmp(str, "True") == 0 || strcmp(str, "False") == 0 ||
        strcmp(str, "1") == 0 || strcmp(str, "0") == 0) {
        return BOOL;
    }
    
    // Test pour entier
    char *endptr;
    long val = strtol(str, &endptr, 10);
    if (*endptr == '\0') {  // Conversion complète réussie
        if (val >= 0) {
            return UINT;  // Entier positif
        } else {
            return INT;   // Entier négatif
        }
    }
    
    // Test pour float
    double fval = strtod(str, &endptr);
    if (*endptr == '\0') {  // Conversion complète réussie
        return FLOAT;
    }
    
    // Par défaut, c'est une chaîne
    return STRING;
}

// Fonction de démonstration
void demonstrate_enum_usage() {
    printf("=== Démonstration des énumérations ===\n\n");
    
    // 1. Déclaration et initialisation
    column_type_t type1 = INT;
    column_type_t type2 = STRING;
    
    printf("Type 1: %s (valeur: %d)\n", column_type_to_string(type1), type1);
    printf("Type 2: %s (valeur: %d)\n", column_type_to_string(type2), type2);
    
    // 2. Utilisation dans un tableau
    column_type_t types[] = {INT, FLOAT, STRING, BOOL};
    int nb_types = sizeof(types) / sizeof(types[0]);
    
    printf("\nTypes dans le tableau:\n");
    for (int i = 0; i < nb_types; i++) {
        printf("  [%d]: %s\n", i, column_type_to_string(types[i]));
    }
    
    // 3. Test de détection de type
    char *test_values[] = {"42", "3.14", "hello", "true", "-10"};
    int nb_values = sizeof(test_values) / sizeof(test_values[0]);
    
    printf("\nDétection automatique de types:\n");
    for (int i = 0; i < nb_values; i++) {
        column_type_t detected = detect_string_type(test_values[i]);
        printf("  '%s' -> %s\n", test_values[i], column_type_to_string(detected));
    }
    
    // 4. Utilisation dans une structure
    dataframe_t df = {0};  // Initialisation à zéro
    df.nb_columns = 3;
    df.column_types = malloc(3 * sizeof(column_type_t));
    
    df.column_types[0] = STRING;
    df.column_types[1] = INT;
    df.column_types[2] = FLOAT;
    
    printf("\nTypes de colonnes du dataframe:\n");
    for (int i = 0; i < df.nb_columns; i++) {
        printf("  Colonne %d: %s\n", i, column_type_to_string(df.column_types[i]));
    }
    
    free(df.column_types);
}

int main() {
    demonstrate_enum_usage();
    return 0;
}
